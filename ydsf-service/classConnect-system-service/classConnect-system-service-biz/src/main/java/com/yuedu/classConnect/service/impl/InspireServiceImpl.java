package com.yuedu.classConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.classConnect.dto.InspireDTO;
import com.yuedu.classConnect.entity.InspireEntity;
import com.yuedu.classConnect.mapper.InspireMapper;
import com.yuedu.classConnect.service.InspireService;
import com.yuedu.classConnect.vo.InspireInfoVO;
import com.yuedu.classConnect.vo.InspireVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteClassTimeStudentsService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsCourseStepDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDTO;
import com.yuedu.ydsf.eduConnect.live.api.feign.RemoteInteractionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 激励表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:53:19
 */
@Slf4j
@Service
@AllArgsConstructor
public class InspireServiceImpl extends ServiceImpl<InspireMapper, InspireEntity> implements InspireService {
    private final RemoteInteractionService remoteInteractionService;
    private final RemoteClassTimeStudentsService remoteClassTimeStudentsService;
    /**
     * 查询学员列表
     *
     * @return List<RollCallVO>
     */
    @Override
    public List<InspireVO> getList(Integer lessonNo, Integer classId,Long storeId) {
        //调取当前出勤学员列表
        R<List<CheckInStudentVO.StudentCheckInInfoVO>> classResult = remoteClassTimeStudentsService.getCheckedInStudentsByLessonNo(Long.valueOf(lessonNo), storeId);
        List<CheckInStudentVO.StudentCheckInInfoVO> classList = classResult.getData();
        List<InspireVO> dataList = baseMapper.selectList(Wrappers.lambdaQuery(InspireEntity.class)
                .eq(InspireEntity::getLessonNo, lessonNo)
        ).stream().map(inspireEntity -> {
            InspireVO inspireVo = new InspireVO();
            BeanUtils.copyProperties(inspireEntity, inspireVo);
            return inspireVo;
        }).toList();

        return inspireList(classList, dataList);
    }
    /**
     * 合并获取激励列表
     *
     * @param classList
     * @param dataList
     * @return
     */
    public List<InspireVO> inspireList(List<CheckInStudentVO.StudentCheckInInfoVO> classList, List<InspireVO> dataList) {
        if (CollUtil.isEmpty(classList) && CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<InspireVO> inspireVos = new ArrayList<>();
        Map<Long, Map<String, Integer>> studentInspireCountMap = new HashMap<>();

        // 统计每个用户的激励信息
        if (CollUtil.isNotEmpty(dataList)) {
            for (InspireVO inspire : dataList) {
                studentInspireCountMap.computeIfAbsent(inspire.getUserId(), k -> new HashMap<>())
                        .merge(inspire.getType(), 1, Integer::sum);
            }
        }
        List<CheckInStudentVO.StudentCheckInInfoVO> studentsToProcess = CollUtil.isNotEmpty(classList) ? classList : dataList.stream()
                .map(inspire -> {
                    CheckInStudentVO.StudentCheckInInfoVO student = new CheckInStudentVO.StudentCheckInInfoVO();
                    student.setStudentId(inspire.getUserId());
                    student.setStudentName(inspire.getUserName());
                    return student;
                }).distinct().toList();
        for (CheckInStudentVO.StudentCheckInInfoVO student : studentsToProcess) {
            InspireVO inspireVO = new InspireVO();

            inspireVO.setUserId(student.getStudentId());
            inspireVO.setUserName(student.getStudentName());

            // 获取该学生的激励信息
            List<InspireInfoVO> inspireInfoList = studentInspireCountMap.getOrDefault(student.getStudentId(), Collections.emptyMap())
                    .entrySet().stream().map(entry -> {
                        InspireInfoVO inspireInfoVO = new InspireInfoVO();
                        inspireInfoVO.setType(entry.getKey());
                        inspireInfoVO.setNum(entry.getValue());
                        return inspireInfoVO;
                    }).toList();
            inspireVO.setInspireInfo(inspireInfoList);
            inspireVos.add(inspireVO);
        }

        return inspireVos;
    }

    /**
     * 发送勋章
     *
     * @param inspireDTO 新增发送勋章
     * @return Boolean
     */
    @Override
    public Boolean saveDetail(InspireDTO inspireDTO) {
        //发送消息给教室端
        SsCourseStepDTO ssCourseStepDTO = new SsCourseStepDTO();
        ssCourseStepDTO.setAttendClassType(inspireDTO.getAttendClassType());
        if (inspireDTO.getAttendClassType() == 0) {
            ssCourseStepDTO.setRoomUUID(inspireDTO.getRoomUUID());
        }
        SsInteractionSettingDTO propertiesDTO = new SsInteractionSettingDTO();
        propertiesDTO.setStudentId(String.valueOf(inspireDTO.getUserId()));
        propertiesDTO.setStudentName(inspireDTO.getUserName());
        propertiesDTO.setIncentiveType(inspireDTO.getType());
        propertiesDTO.setCreator(SecurityUtils.getUser().getUsername());
        propertiesDTO.setCreatorId(SecurityUtils.getUser().getId());
        propertiesDTO.setCtime(LocalDateTime.now());
        ssCourseStepDTO.setProperties(propertiesDTO);
        ssCourseStepDTO.setTimeTableId(inspireDTO.getTimeTableId());

        InspireEntity inspireEntity = new InspireEntity();
        BeanUtil.copyProperties(inspireDTO, inspireEntity);

        R response =  remoteInteractionService.medal(ssCourseStepDTO);
        if (response.isOk()) {
            save(inspireEntity);
        } else {
            throw new BizException(response.getMsg());
        }
        return Boolean.TRUE;
    }
    /**
     * 查询获得勋章的学员列表
     *
     * @return List<RollCallVO>
     */
    @Override
    public List<InspireVO> getMedalList(Integer lessonNo) {

        List<InspireVO> dataList = baseMapper.selectList(Wrappers.lambdaQuery(InspireEntity.class)
                .eq(InspireEntity::getLessonNo, lessonNo)
        ).stream().map(inspireEntity -> {
            InspireVO inspireVo = new InspireVO();
            BeanUtils.copyProperties(inspireEntity, inspireVo);
            return inspireVo;
        }).toList();
        return inspireList(Collections.emptyList(), dataList);
    }
}
